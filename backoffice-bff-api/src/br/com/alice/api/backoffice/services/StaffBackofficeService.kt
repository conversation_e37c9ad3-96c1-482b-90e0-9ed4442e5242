package br.com.alice.api.backoffice.services

import br.com.alice.common.core.Role
import br.com.alice.common.core.StaffType
import br.com.alice.common.models.CouncilType
import br.com.alice.common.models.SpecialistTier
import br.com.alice.data.layer.models.HealthSpecialistScoreEnum
import br.com.alice.api.backoffice.transfers.*
import br.com.alice.api.backoffice.transfers.staff.CouncilTypeResponse
import br.com.alice.api.backoffice.transfers.staff.StaffRolesResponse
import br.com.alice.api.backoffice.transfers.staff.StaffScoreResponse
import br.com.alice.api.backoffice.transfers.staff.StaffTiersResponse

class StaffBackofficeService() {

    fun getStaffRoles(staffType: StaffType): List<StaffRolesResponse> {
        val roles = Role.values()
            .filter { it.types.contains(staffType) }
            .map { role ->
                StaffRolesResponse(
                    id = role.name,
                    name = role.description,
                    value = role.name
                )
            }
        return roles
    }

    fun getStaffTiers(): List<StaffTiersResponse> {
        val tiers = SpecialistTier
            .values()
            .map { tier ->
                StaffTiersResponse(
                    id = tier.name,
                    name = tier.description,
                    value = tier.name
                )
            }
        return tiers
    }

    fun getStaffScore(): List<StaffScoreResponse> {
        val scores = HealthSpecialistScoreEnum
            .values()
            .map { score ->
                StaffScoreResponse(
                    id = score.name,
                    name = score.description,
                    value = score.name
                )
            }
        return scores
    }

    fun getCouncilTypes(): List<CouncilTypeResponse> {
        val councilTypes = CouncilType
            .values()
            .map { type ->
                CouncilTypeResponse(
                    id = type.name,
                    name = type.name
                )
            }
        return councilTypes
    }

}
