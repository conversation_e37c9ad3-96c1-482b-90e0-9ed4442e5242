package br.com.alice.api.backoffice.mappers.staff

import br.com.alice.api.backoffice.transfers.staff.CouncilDTO
import br.com.alice.api.backoffice.transfers.staff.CreateStaffRequest
import br.com.alice.api.backoffice.transfers.staff.UpdateStaffRequest
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Role
import br.com.alice.common.core.StaffType
import br.com.alice.common.models.CouncilType
import br.com.alice.common.models.Gender
import br.com.alice.common.models.State
import br.com.alice.data.layer.models.Council
import br.com.alice.data.layer.models.HealthProfessional
import br.com.alice.data.layer.models.Staff
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Test
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

class StaffCouncilMappingTest {

    @Test
    fun `StaffInputMapper should map CouncilDTO with CouncilType enum to Council model`() {
        val councilDTO = CouncilDTO(
            number = "123456",
            state = State.SP,
            type = CouncilType.CRM
        )

        val createRequest = CreateStaffRequest(
            firstName = "João",
            lastName = "Silva",
            email = "<EMAIL>",
            gender = Gender.MALE,
            role = Role.CHIEF_PHYSICIAN,
            type = StaffType.HEALTH_PROFESSIONAL,
            council = councilDTO
        )

        val staffId = RangeUUID.generate()
        val healthProfessional = StaffInputMapper.toHealthProfessional(createRequest, staffId)

        assertNotNull(healthProfessional)
        assertEquals("123456", healthProfessional?.council?.number)
        assertEquals(State.SP, healthProfessional?.council?.state)
        assertEquals(CouncilType.CRM, healthProfessional?.council?.type)
    }

    @Test
    fun `StaffInputMapper should handle null CouncilType in CouncilDTO`() {
        val councilDTO = CouncilDTO(
            number = "123456",
            state = State.RJ,
            type = null
        )

        val createRequest = CreateStaffRequest(
            firstName = "Maria",
            lastName = "Santos",
            email = "<EMAIL>",
            gender = Gender.FEMALE,
            role = Role.CHIEF_PHYSICIAN,
            type = StaffType.HEALTH_PROFESSIONAL,
            council = councilDTO
        )

        val staffId = RangeUUID.generate()
        val healthProfessional = StaffInputMapper.toHealthProfessional(createRequest, staffId)

        assertNotNull(healthProfessional)
        assertEquals("123456", healthProfessional?.council?.number)
        assertEquals(State.RJ, healthProfessional?.council?.state)
        assertNull(healthProfessional?.council?.type)
    }

    @Test
    fun `StaffInputMapper should handle null council in CreateStaffRequest`() {
        val createRequest = CreateStaffRequest(
            firstName = "Pedro",
            lastName = "Costa",
            email = "<EMAIL>",
            gender = Gender.MALE,
            role = Role.CHIEF_PHYSICIAN,
            type = StaffType.HEALTH_PROFESSIONAL,
            council = null
        )

        val staffId = RangeUUID.generate()
        val healthProfessional = StaffInputMapper.toHealthProfessional(createRequest, staffId)

        assertNotNull(healthProfessional)
        assertEquals("", healthProfessional?.council?.number)
        assertEquals(State.SP, healthProfessional?.council?.state)
        assertNull(healthProfessional?.council?.type)
    }

    @Test
    fun `StaffInputMapper should map UpdateStaffRequest with CouncilType enum`() {
        val councilDTO = CouncilDTO(
            number = "654321",
            state = State.MG,
            type = CouncilType.COREN
        )

        val updateRequest = UpdateStaffRequest(
            firstName = "Ana",
            lastName = "Oliveira",
            email = "<EMAIL>",
            gender = Gender.FEMALE,
            role = Role.CARE_COORD_NURSE,
            type = StaffType.HEALTH_PROFESSIONAL,
            council = councilDTO
        )

        val staffId = RangeUUID.generate()
        val existingHealthProfessionalId = RangeUUID.generate()
        val healthProfessional = StaffInputMapper.toHealthProfessionalForUpdate(
            updateRequest, 
            staffId, 
            existingHealthProfessionalId
        )

        assertNotNull(healthProfessional)
        assertEquals("654321", healthProfessional?.council?.number)
        assertEquals(State.MG, healthProfessional?.council?.state)
        assertEquals(CouncilType.COREN, healthProfessional?.council?.type)
    }

    @Test
    fun `StaffOutputMapper should map Council model to CouncilDTO with CouncilType enum`() {
        val council = Council(
            number = "789012",
            state = State.RS,
            type = CouncilType.CRF
        )

        val healthProfessional = HealthProfessional(
            id = RangeUUID.generate(),
            staffId = RangeUUID.generate(),
            council = council,
            createdAt = LocalDateTime.now(),
            updatedAt = LocalDateTime.now()
        )

        val staff = Staff(
            id = RangeUUID.generate(),
            firstName = "Carlos",
            lastName = "Ferreira",
            email = "<EMAIL>",
            gender = Gender.MALE,
            role = Role.PHARMACIST,
            type = StaffType.HEALTH_PROFESSIONAL,
            active = true,
            version = 1,
            createdAt = LocalDateTime.now(),
            updatedAt = LocalDateTime.now()
        )

        val result = StaffOutputMapper.toFullResponse(staff, healthProfessional)

        assertNotNull(result.council)
        assertEquals("789012", result.council?.number)
        assertEquals(State.RS, result.council?.state)
        assertEquals(CouncilType.CRF, result.council?.type)
    }

    @Test
    fun `StaffOutputMapper should handle null CouncilType in Council model`() {
        val council = Council(
            number = "345678",
            state = State.BA,
            type = null
        )

        val healthProfessional = HealthProfessional(
            id = RangeUUID.generate(),
            staffId = RangeUUID.generate(),
            council = council,
            createdAt = LocalDateTime.now(),
            updatedAt = LocalDateTime.now()
        )

        val staff = Staff(
            id = RangeUUID.generate(),
            firstName = "Lucia",
            lastName = "Almeida",
            email = "<EMAIL>",
            gender = Gender.FEMALE,
            role = Role.CHIEF_PHYSICIAN,
            type = StaffType.HEALTH_PROFESSIONAL,
            active = true,
            version = 1,
            createdAt = LocalDateTime.now(),
            updatedAt = LocalDateTime.now()
        )

        val result = StaffOutputMapper.toFullResponse(staff, healthProfessional)

        assertNotNull(result.council)
        assertEquals("345678", result.council?.number)
        assertEquals(State.BA, result.council?.state)
        assertNull(result.council?.type)
    }

    @Test
    fun `should test all CouncilType enum values mapping correctly`() {
        CouncilType.values().forEach { councilType ->
            val councilDTO = CouncilDTO(
                number = "123456",
                state = State.SP,
                type = councilType
            )

            val createRequest = CreateStaffRequest(
                firstName = "Test",
                lastName = "User",
                email = "<EMAIL>",
                gender = Gender.MALE,
                role = Role.CHIEF_PHYSICIAN,
                type = StaffType.HEALTH_PROFESSIONAL,
                council = councilDTO
            )

            val staffId = RangeUUID.generate()
            val healthProfessional = StaffInputMapper.toHealthProfessional(createRequest, staffId)

            assertNotNull(healthProfessional)
            assertEquals(councilType, healthProfessional?.council?.type)

            // Test reverse mapping
            val staff = Staff(
                id = RangeUUID.generate(),
                firstName = "Test",
                lastName = "User",
                email = "<EMAIL>",
                gender = Gender.MALE,
                role = Role.CHIEF_PHYSICIAN,
                type = StaffType.HEALTH_PROFESSIONAL,
                active = true,
                version = 1,
                createdAt = LocalDateTime.now(),
                updatedAt = LocalDateTime.now()
            )

            val result = StaffOutputMapper.toFullResponse(staff, healthProfessional)
            assertEquals(councilType, result.council?.type)
        }
    }
}
